export const mockData = [
  {
    workOrderNo: 'WO2024001',
    productSpec: 'A-100',
    productName: '铝合金外壳',
    status: '已完成',
    progressBar: 100,
    planStartTime: '2024-06-01 08:00',
    planEndTime: '2024-06-05 18:00',
    images: ['img1.png', 'img2.png'],
    relatedDocs: ['DOC001'],
    planQuantity: 1000,
    actualQuantity: 980,
    goodQuantity: 970,
    defectQuantity: 10,
    remark: '首批试产',
    createUser: '张三',
    updateUser: '李四',
    unit: '件',
    createTime: '2024-05-30 09:00',
    updateTime: '2024-06-05 19:00',
    actualStartTime: '2024-06-01 08:30',
    actualEndTime: '2024-06-05 17:50',
    totalWorkHours: 120,
    customer: 'A公司',
    productWeight: 1.2,
    loss: 5,
    relation: '无',
    currentProcess: '包装',
    salesOrder: 'SO2024001',
    remainingQuantity: 20,
    taskQuantity: 5,
    processProgress: 100,
    processProgressPercent: 100,
    workOrderProgress: 100,
    workOrderProgressPercent: 100,
    timeProgressPercent: 100,
    totalSalary: 5000,
    scheduleStatus: '正常',
    planProductionCycle: 5,
    actualProductionCycle: 5,
    productionCycleRatio: 1,
    action: '',
  },
  {
    workOrderNo: 'WO2024002',
    productSpec: 'B-200',
    productName: '电机壳体',
    status: '进行中',
    progressBar: 60,
    planStartTime: '2024-06-02 08:00',
    planEndTime: '2024-06-06 18:00',
    images: ['img3.png'],
    relatedDocs: ['DOC002'],
    planQuantity: 800,
    actualQuantity: 600,
    goodQuantity: 590,
    defectQuantity: 10,
    remark: '需重点关注',
    createUser: '王五',
    updateUser: '赵六',
    unit: '件',
    createTime: '2024-05-31 09:00',
    updateTime: '2024-06-04 19:00',
    actualStartTime: '2024-06-02 08:30',
    actualEndTime: '',
    totalWorkHours: 80,
    customer: 'B公司',
    productWeight: 1.5,
    loss: 8,
    relation: '有',
    currentProcess: '喷涂',
    salesOrder: 'SO2024002',
    remainingQuantity: 200,
    taskQuantity: 10,
    processProgress: 60,
    processProgressPercent: 60,
    workOrderProgress: 60,
    workOrderProgressPercent: 60,
    timeProgressPercent: 70,
    totalSalary: 3000,
    scheduleStatus: '延误',
    planProductionCycle: 4,
    actualProductionCycle: 5,
    productionCycleRatio: 1.25,
    action: '',
  },
  {
    workOrderNo: 'WO2024026',
    productSpec: 'Z-2600',
    productName: '精密齿轮',
    status: '未开始',
    progressBar: 0,
    planStartTime: '2024-07-01 08:00',
    planEndTime: '2024-07-05 18:00',
    images: [],
    relatedDocs: [],
    planQuantity: 500,
    actualQuantity: 0,
    goodQuantity: 0,
    defectQuantity: 0,
    remark: '',
    createUser: '管理员',
    updateUser: '管理员',
    unit: '件',
    createTime: '2024-06-25 09:00',
    updateTime: '2024-06-25 09:00',
    actualStartTime: '',
    actualEndTime: '',
    totalWorkHours: 0,
    customer: 'Z公司',
    productWeight: 0.8,
    loss: 0,
    relation: '无',
    currentProcess: '未分配',
    salesOrder: 'SO2024026',
    remainingQuantity: 500,
    taskQuantity: 0,
    processProgress: 0,
    processProgressPercent: 0,
    workOrderProgress: 0,
    workOrderProgressPercent: 0,
    timeProgressPercent: 0,
    totalSalary: 0,
    scheduleStatus: '未开始',
    planProductionCycle: 5,
    actualProductionCycle: 0,
    productionCycleRatio: 0,
    action: '',
  },
]
