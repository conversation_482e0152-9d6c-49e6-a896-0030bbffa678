.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .drawer-header-title {
    font-size: 16px;
    font-weight: 600;
    color: #111;
  }

  .drawer-header-btn {
    display: flex;
    align-items: center;
  }
}

.drawer-content {
  padding: 24px;

  .drawer-content-item {
    margin-bottom: 16px;

    &__title {
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 600;
    }
  }
}

.drawer-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;

  .drawer-footer__btn {
    min-width: 80px;
  }
}

.detail-box {
  display: flex;
  margin-top: 4px;

  .info-block {
    background-color: #f8f9fa;
    border-radius: 6px;
    height: 76px;
    padding: 8px;
    overflow-y: auto;
    margin-right: 4px;

    &:last-child {
      margin-right: 0;
    }
  }
}

/* 主要解决方案: 使用 !important 提高优先级 */
:deep(.drawer-header-tabs .ant-tabs-nav) {
  margin-bottom: 0;
}

:deep(.drawer-content-item__content .ant-form-item-label > label) {
  height: 30px;
}

:deep(label[title]) {
  display: flex;
  align-items: center;

  > div {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  .plan-time {
    font-size: 12px;
    color: $primary-color;
  }

  .product-info .product-info-btn {
    font-weight: 600;
    color: #328eff;
    cursor: pointer;
  }
}

:deep(.ant-form-item-extra) {
  .product-detail {
    .product-detail__left {
      flex: 2 0;
      display: flex;
      flex-direction: column;
      margin-right: 4px;

      .product-row {
        margin-bottom: 8px;
        font-size: 12px;
        line-height: 14px;

        &:last-child {
          margin-bottom: 0;
        }

        &__label {
          color: #999;
          margin-right: 8px;
        }

        &__value {
          color: #333;
          word-break: break-all;
        }
      }
    }

    .product-detail__right {
      color: #666;
      flex-direction: column;
      flex: 1 0;
      justify-content: center;
      align-items: center;
      width: 100%;
      display: flex;

      .stock-amount {
        text-align: center;
        color: #666;
        width: 100%;
        margin-bottom: 6px;
        font-family: DINAlternate-Bold, DINAlternate;
        font-size: 18px;
        font-weight: 700;
        line-height: 18px;
      }

      .stock-unit-text {
        white-space: nowrap;
        text-overflow: ellipsis;
        justify-content: center;
        width: 100%;
        padding: 0 3px;
        font-size: 12px;
        line-height: 14px;
        display: flex;
        overflow: hidden;
      }
    }
  }

  .plan-count__item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .amount {
      text-align: center;
      margin-bottom: 6px;
      font-family: DINAlternate-Bold, DINAlternate;
      font-size: 18px;
      font-weight: 700;
      line-height: 18px;

      &.good {
        color: $primary-color;
      }
      &.bad {
        color: $error-color;
      }
      &.rate {
        color: $black;
      }
    }

    .label {
      color: #666;
      font-size: 12px;
      line-height: 14px;
    }
  }
}

/* 备注文本域样式调整 */
:deep(.ant-row) {
  align-items: stretch; /* 让行内的列高度保持一致 */
}
