.work-order-page {
  height: 100%;
  overflow: auto;
}

.search-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
  margin-top: $spacing-base;
  padding: 0 $spacing-lg;
}

.filter-row {
  margin-top: $spacing-base;
  padding: 0 $spacing-lg;

  .form-wrapper {
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
    display: flex;

    .filter-list {
      flex: 1;
      overflow: hidden;
    }

    .form-actions {
      align-items: center;
      gap: 12px;
      display: flex;
      margin-left: 10px;
    }
  }

  .expand-wrapper {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: -$spacing-sm;
    margin-left: -$spacing-lg;
    margin-right: -$spacing-lg;

    .expand-line {
      position: absolute;
      width: 100%;
      height: 1px;
      background-color: #e8e8e8;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }

    .expand-icon {
      position: relative;
      z-index: 1;
      background-color: #fff;
      width: 40px;
      height: 14px;
      color: #666;
      border: 1px solid #eee;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      border-radius: 8px;
    }
  }
}

.list-action-wrapper {
  flex-shrink: 0; // 防止搜索区域被压缩
}

.list-table-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin-top: $spacing-md;
  padding: 0 $spacing-lg $spacing-md;

  .list-table-header {
    flex-shrink: 0; // 防止表格头部被压缩
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-md;

    &__left,
    &__right {
      display: flex;
      align-items: center;
      gap: $spacing-base;
    }

    &__right {
      .custom-btn {
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }
  }

  .list-table-body {
    flex: 1;
    overflow: hidden; // 让表格组件自己处理滚动
    min-height: 0; // 确保flex子元素可以收缩
  }
}

:deep(.list-table-header__left .ant-btn-text) {
  padding: 4px 8px;
}

:deep(.list-table-body .ant-btn-link) {
  padding: 4px 8px;
}

// 字段配置下拉面板样式
.column-config-dropdown {
  padding: 0;

  :deep(.ant-dropdown-menu) {
    padding: 0;
    box-shadow: none;
  }
}

// 确保下拉菜单不会因为内部点击而关闭
:deep(.ant-dropdown) {
  .ant-dropdown-menu {
    .ant-dropdown-menu-item {
      padding: 0;
    }
  }
}
