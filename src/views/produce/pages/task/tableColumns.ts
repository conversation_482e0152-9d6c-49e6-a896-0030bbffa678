export interface TableColumn {
  title: string
  dataIndex?: string
  key: string
  width?: number
  visible: boolean
  fixed?: boolean | 'left' | 'right'
  order: number // 用于排序
  icon?: string // 字段图标
}

export const tableColumns: TableColumn[] = [
  {
    title: '工单编号',
    dataIndex: 'workOrderNo',
    key: 'workOrderNo',
    visible: true,
    width: 120,
    fixed: 'left',
    order: 1,
    icon: 'file-text',
  },
  {
    title: '产品规格',
    dataIndex: 'productSpec',
    key: 'productSpec',
    visible: true,
    width: 120,
    fixed: 'left',
    order: 2,
    icon: 'profile',
  },
  {
    title: '产品名称',
    dataIndex: 'productName',
    key: 'productName',
    visible: true,
    width: 150,
    fixed: 'left',
    order: 3,
    icon: 'file-text',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    visible: true,
    width: 80,
    order: 4,
    icon: 'flag',
  },
  {
    title: '工单进度条',
    dataIndex: 'progressBar',
    key: 'progressBar',
    visible: true,
    width: 400,
    order: 5,
    icon: 'progress',
  },
  {
    title: '计划开始时间',
    dataIndex: 'planStartTime',
    key: 'planStartTime',
    visible: true,
    width: 180,
    order: 6,
    icon: 'calendar',
  },
  {
    title: '计划结束时间',
    dataIndex: 'planEndTime',
    key: 'planEndTime',
    visible: true,
    width: 180,
    order: 7,
    icon: 'calendar',
  },
  {
    title: '图片/图纸',
    dataIndex: 'images',
    key: 'images',
    visible: true,
    width: 100,
    order: 8,
    icon: 'picture',
  },
  {
    title: '关联单据',
    dataIndex: 'relatedDocs',
    key: 'relatedDocs',
    visible: true,
    width: 100,
    order: 9,
    icon: 'link',
  },
  {
    title: '计划数',
    dataIndex: 'planQuantity',
    key: 'planQuantity',
    visible: true,
    width: 80,
    order: 10,
    icon: 'number',
  },
  {
    title: '实际数',
    dataIndex: 'actualQuantity',
    key: 'actualQuantity',
    visible: true,
    width: 80,
    order: 11,
    icon: 'number',
  },
  {
    title: '良品数',
    dataIndex: 'goodQuantity',
    key: 'goodQuantity',
    visible: true,
    width: 80,
    order: 12,
    icon: 'check-circle',
  },
  {
    title: '不良品数',
    dataIndex: 'defectQuantity',
    key: 'defectQuantity',
    visible: true,
    width: 100,
    order: 13,
    icon: 'close-circle',
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    visible: true,
    width: 120,
    order: 14,
    icon: 'file-text',
  },
  {
    title: '创建人',
    dataIndex: 'createUser',
    key: 'createUser',
    visible: true,
    width: 100,
    order: 15,
    icon: 'user',
  },
  {
    title: '更新人',
    dataIndex: 'updateUser',
    key: 'updateUser',
    visible: true,
    width: 100,
    order: 16,
    icon: 'user',
  },
  {
    title: '单位',
    dataIndex: 'unit',
    key: 'unit',
    visible: true,
    width: 60,
    order: 17,
    icon: 'tag',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    visible: true,
    width: 150,
    order: 18,
    icon: 'clock-circle',
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    visible: true,
    width: 150,
    order: 19,
    icon: 'clock-circle',
  },
  {
    title: '实际开始时间',
    dataIndex: 'actualStartTime',
    key: 'actualStartTime',
    visible: true,
    width: 150,
    order: 20,
    icon: 'play-circle',
  },
  {
    title: '实际结束时间',
    dataIndex: 'actualEndTime',
    key: 'actualEndTime',
    visible: true,
    width: 150,
    order: 21,
    icon: 'stop',
  },
  {
    title: '累计工时',
    dataIndex: 'totalWorkHours',
    key: 'totalWorkHours',
    visible: true,
    width: 100,
    order: 22,
    icon: 'clock-circle',
  },
  {
    title: '登录客户',
    dataIndex: 'customer',
    key: 'customer',
    visible: true,
    width: 100,
    order: 23,
    icon: 'user',
  },
  {
    title: '产品单重',
    dataIndex: 'productWeight',
    key: 'productWeight',
    visible: true,
    width: 100,
    order: 24,
    icon: 'dashboard',
  },
  {
    title: '损耗',
    dataIndex: 'loss',
    key: 'loss',
    visible: true,
    width: 80,
    order: 25,
    icon: 'minus-circle',
  },
  {
    title: '关联',
    dataIndex: 'relation',
    key: 'relation',
    visible: true,
    width: 80,
    order: 26,
    icon: 'link',
  },
  {
    title: '当前工序',
    dataIndex: 'currentProcess',
    key: 'currentProcess',
    visible: true,
    width: 100,
    order: 27,
    icon: 'setting',
  },
  {
    title: '关联销售订单',
    dataIndex: 'salesOrder',
    key: 'salesOrder',
    visible: true,
    width: 150,
    order: 28,
    icon: 'shopping-cart',
  },
  {
    title: '剩余数',
    dataIndex: 'remainingQuantity',
    key: 'remainingQuantity',
    visible: true,
    width: 80,
    order: 29,
    icon: 'number',
  },
  {
    title: '任务数',
    dataIndex: 'taskQuantity',
    key: 'taskQuantity',
    visible: true,
    width: 80,
    order: 30,
    icon: 'number',
  },
  {
    title: '工序进度',
    dataIndex: 'processProgress',
    key: 'processProgress',
    visible: true,
    width: 100,
    order: 31,
    icon: 'progress',
  },
  {
    title: '工序进度(%)',
    dataIndex: 'processProgressPercent',
    key: 'processProgressPercent',
    visible: true,
    width: 120,
    order: 32,
    icon: 'percentage',
  },
  {
    title: '工单进度',
    dataIndex: 'workOrderProgress',
    key: 'workOrderProgress',
    visible: true,
    width: 100,
    order: 33,
    icon: 'progress',
  },
  {
    title: '工单进度(%)',
    dataIndex: 'workOrderProgressPercent',
    key: 'workOrderProgressPercent',
    visible: true,
    width: 120,
    order: 34,
    icon: 'percentage',
  },
  {
    title: '时间进度(%)',
    dataIndex: 'timeProgressPercent',
    key: 'timeProgressPercent',
    visible: true,
    width: 120,
    order: 35,
    icon: 'percentage',
  },
  {
    title: '累计工资',
    dataIndex: 'totalSalary',
    key: 'totalSalary',
    visible: true,
    width: 100,
    order: 36,
    icon: 'dollar',
  },
  {
    title: '工期状态',
    dataIndex: 'scheduleStatus',
    key: 'scheduleStatus',
    visible: true,
    width: 100,
    order: 37,
    icon: 'flag',
  },
  {
    title: '计划生产周期',
    dataIndex: 'planProductionCycle',
    key: 'planProductionCycle',
    visible: true,
    width: 130,
    order: 38,
    icon: 'calendar',
  },
  {
    title: '实际生产周期',
    dataIndex: 'actualProductionCycle',
    key: 'actualProductionCycle',
    visible: true,
    width: 130,
    order: 39,
    icon: 'calendar',
  },
  {
    title: '生产周期倍率',
    dataIndex: 'productionCycleRatio',
    key: 'productionCycleRatio',
    visible: true,
    width: 130,
    order: 40,
    icon: 'calculator',
  },
  {
    title: '操作',
    key: 'action',
    visible: true,
    width: 180,
    fixed: 'right',
    order: 999,
    icon: 'setting',
  },
]
