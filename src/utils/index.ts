import dayjs from 'dayjs'
// 获取剩余时间
export function getRemainingTime(endTime: dayjs.Dayjs) {
  const now = dayjs()
  const diffInMinutes = endTime.diff(now, 'minute')

  // 如果已经过期
  if (diffInMinutes <= 0) {
    return '已过期'
  }

  // 不足1小时，显示分钟
  if (diffInMinutes < 60) {
    return `${diffInMinutes}分钟`
  }

  // 不足1天但大于1小时，显示小时
  if (diffInMinutes < 24 * 60) {
    const hours = Math.floor(diffInMinutes / 60)

    return `${hours}小时`
  }

  // 大于1天，显示天数和小时
  const days = Math.floor(diffInMinutes / (24 * 60))
  const remainingMinutes = diffInMinutes % (24 * 60)
  const hours = Math.floor(remainingMinutes / 60)

  if (hours === 0) {
    return `${days}天`
  }
  return `${days}天${hours}小时`
}

// 千分位格式化
export function formatNumber(num: number | string | undefined | null, locale = 'en-US'): string {
  if (num === null || num === undefined || isNaN(Number(num))) return '-'
  return new Intl.NumberFormat(locale).format(Number(num))
}
